package com.cgtiger.erp.module.shuhu.dal.dataobject.boqquote;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cgtiger.erp.module.shuhu.dal.dataobject.demand.DemandDO;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cgtiger.erp.framework.mybatis.core.dataobject.BaseDO;

/**
 * BOQ报价 DO
 *
 * <AUTHOR>
 */
@TableName("sh_quote")
@KeySequence("sh_quote_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BoqQuoteDO extends BaseDO {

}