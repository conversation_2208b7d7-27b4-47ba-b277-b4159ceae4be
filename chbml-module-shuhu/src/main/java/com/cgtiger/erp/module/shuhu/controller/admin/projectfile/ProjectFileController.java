package com.cgtiger.erp.module.shuhu.controller.admin.projectfile;

import com.cgtiger.erp.module.shuhu.enums.ProjectFilesDefEnum;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.util.*;
import java.io.IOException;

import com.cgtiger.erp.framework.common.pojo.PageParam;
import com.cgtiger.erp.framework.common.pojo.PageResult;
import com.cgtiger.erp.framework.common.pojo.CommonResult;
import com.cgtiger.erp.framework.common.util.object.BeanUtils;

import static com.cgtiger.erp.framework.common.pojo.CommonResult.success;

import com.cgtiger.erp.framework.excel.core.util.ExcelUtils;

import com.cgtiger.erp.framework.apilog.core.annotation.ApiAccessLog;

import static com.cgtiger.erp.framework.apilog.core.enums.OperateTypeEnum.*;

import com.cgtiger.erp.module.shuhu.controller.admin.projectfile.vo.*;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projectfile.ProjectFileDO;
import com.cgtiger.erp.module.shuhu.service.projectfile.ProjectFileService;

@Tag(name = "管理后台 - 项目附件")
@RestController
@RequestMapping("/shuhu/project-file")
@Validated
public class ProjectFileController {

    @Resource
    private ProjectFileService projectFileService;

    @PostMapping("/create")
    @Operation(summary = "创建项目附件")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:create')")
    public CommonResult<Long> createProjectFile(@Valid @RequestBody ProjectFileSaveReqVO createReqVO) {
        return success(projectFileService.createProjectFile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新项目附件")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:update')")
    public CommonResult<Boolean> updateProjectFile(@Valid @RequestBody ProjectFileSaveReqVO updateReqVO) {
        projectFileService.updateProjectFile(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除项目附件")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:delete')")
    public CommonResult<Boolean> deleteProjectFile(@RequestParam("id") Long id) {
        projectFileService.deleteProjectFile(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除项目附件")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:delete')")
    public CommonResult<Boolean> deleteProjectFileList(@RequestParam("ids") List<Long> ids) {
        projectFileService.deleteProjectFileListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目附件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<ProjectFileRespVO> getProjectFile(@RequestParam("id") Long id) {
        ProjectFileDO projectFile = projectFileService.getProjectFile(id);
        return success(BeanUtils.toBean(projectFile, ProjectFileRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目附件分页")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<PageResult<ProjectFileRespVO>> getProjectFilePage(@Valid ProjectFilePageReqVO pageReqVO) {
        PageResult<ProjectFileDO> pageResult = projectFileService.getProjectFilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProjectFileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出项目附件 Excel")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProjectFileExcel(@Valid ProjectFilePageReqVO pageReqVO,
                                       HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProjectFileDO> list = projectFileService.getProjectFilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "项目附件.xls", "数据", ProjectFileRespVO.class,
                BeanUtils.toBean(list, ProjectFileRespVO.class));
    }

    @GetMapping("/get-upload-file-items")
    @Operation(summary = "上传时根据场景和级别获取附件定义")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<List<ProjectFilesDefEnum>> getUploadFileItems(@RequestParam("scene") String scene, @RequestParam("level") String level) {
        List<ProjectFilesDefEnum> fileItems = projectFileService.getUploadFileItems(scene, level);
        return success(fileItems);
    }

    @GetMapping("/all-upload-file-items")
    @Operation(summary = "获取所有文件定义列表")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<List<ProjectFilesDefEnum>> getAllFileDefinitions() {
        List<ProjectFilesDefEnum> definitions = projectFileService.getAllFileDefinitions();
        return success(definitions);
    }

    @GetMapping("/get-preview-file-items")
    @Operation(summary = "预览时根据场景、级别、业务Key、项目ID 获取附件列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<List<ProjectFileRespVO>> getPreviewFileItems(@RequestParam("scene") String scene,
                                                                     @RequestParam("level") String level,
                                                                     @RequestParam("businessKey") String businessKey,
                                                                     @RequestParam("projectId") Long projectId) {
        List<ProjectFileRespVO> fileItems = projectFileService.getPreviewFileItems(scene, level, businessKey, projectId);
        return success(fileItems);
    }

    @GetMapping("/list-by-project-id")
    @Operation(summary = "根据项目ID获取所有项目附件")
    @Parameter(name = "projectId", description = "项目ID", required = true)
    @PreAuthorize("@ss.hasPermission('shuhu:project-file:query')")
    public CommonResult<List<ProjectFileRespVO>> getProjectFilesByProjectId(@RequestParam("projectId") Long projectId) {
        List<ProjectFileDO> files = projectFileService.getProjectFilesByProjectId(projectId);
        return success(BeanUtils.toBean(files, ProjectFileRespVO.class));
    }
}
