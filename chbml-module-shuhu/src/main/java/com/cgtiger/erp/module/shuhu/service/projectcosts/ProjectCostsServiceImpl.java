package com.cgtiger.erp.module.shuhu.service.projectcosts;

import cn.hutool.core.collection.CollUtil;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projects.ProjectsDO;
import com.cgtiger.erp.module.shuhu.dal.mysql.projects.ProjectsMapper;
import com.cgtiger.erp.module.shuhu.enums.ProjectCostsEnum;
import com.cgtiger.erp.module.shuhu.service.projects.ProjectsService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import com.cgtiger.erp.module.shuhu.controller.admin.projectcosts.vo.*;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projectcosts.ProjectCostsDO;
import com.cgtiger.erp.framework.common.pojo.PageResult;
import com.cgtiger.erp.framework.common.pojo.PageParam;
import com.cgtiger.erp.framework.common.util.object.BeanUtils;

import com.cgtiger.erp.module.shuhu.dal.mysql.projectcosts.ProjectCostsMapper;

import static com.cgtiger.erp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cgtiger.erp.framework.common.util.collection.CollectionUtils.convertList;
import static com.cgtiger.erp.framework.common.util.collection.CollectionUtils.diffList;
import static com.cgtiger.erp.module.shuhu.enums.ErrorCodeConstants.*;

/**
 * 项目费用明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectCostsServiceImpl implements ProjectCostsService {

    @Resource
    private ProjectCostsMapper projectCostsMapper;

    @Resource
    private ProjectsService projectsService;

    @Override
    public Long createProjectCosts(ProjectCostsSaveReqVO createReqVO) {
        // 插入
        ProjectCostsDO projectCosts = BeanUtils.toBean(createReqVO, ProjectCostsDO.class);
        projectCostsMapper.insert(projectCosts);
        // 返回
        return projectCosts.getId();
    }

    @Override
    public void updateProjectCosts(ProjectCostsSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectCostsExists(updateReqVO.getId());
        // 更新
        ProjectCostsDO updateObj = BeanUtils.toBean(updateReqVO, ProjectCostsDO.class);
        projectCostsMapper.updateById(updateObj);
    }

    @Override
    public void deleteProjectCosts(Long id) {
        // 校验存在
        validateProjectCostsExists(id);
        // 删除
        projectCostsMapper.deleteById(id);
    }

    @Override
        public void deleteProjectCostsListByIds(List<Long> ids) {
        // 校验存在
        validateProjectCostsExists(ids);
        // 删除
        projectCostsMapper.deleteByIds(ids);
        }

    private void validateProjectCostsExists(List<Long> ids) {
        List<ProjectCostsDO> list = projectCostsMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(PROJECT_COSTS_NOT_EXISTS);
        }
    }

    private void validateProjectCostsExists(Long id) {
        if (projectCostsMapper.selectById(id) == null) {
            throw exception(PROJECT_COSTS_NOT_EXISTS);
        }
    }

    @Override
    public ProjectCostsDO getProjectCosts(Long id) {
        return projectCostsMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectCostsDO> getProjectCostsPage(ProjectCostsPageReqVO pageReqVO) {
        // 获取项目信息
        ProjectsDO project = projectsService.getProjects(Long.parseLong(pageReqVO.getProjectId()));
        if (project == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }

        // 记录清除ID
        List<Integer> clearIds = new ArrayList<>();
        // 去掉项目id
        pageReqVO.setProjectId(null);
        // 分页查询
        PageResult<ProjectCostsDO> pageResult = projectCostsMapper.selectPage(pageReqVO);
        PageResult<ProjectCostsDO> pageNewResult = projectCostsMapper.selectPage(pageReqVO);

        if (pageResult.getList() == null) {
            throw exception(PROJECT_COSTS_NOT_EXISTS);
        }else {
            for (ProjectCostsDO projectCostsDO : pageResult.getList()) {
                if (projectCostsDO.getClassify().equals(ProjectCostsEnum.OVERHEAD.getStatus())) {
                    // 计算公司管理费：按立项总报价的15%计算
                    BigDecimal companyManagementFee = project.getSetupQuote().multiply(new BigDecimal(15)).divide(new BigDecimal(100));
                    projectCostsDO.setQuotation(companyManagementFee);
                } else if (projectCostsDO.getClassify().equals(ProjectCostsEnum.INTERNAL_MANAGEMENT.getStatus())) {
                    // 计算内部管理：按立项总报价的10%计算
                    BigDecimal internalManagementFee = project.getSetupQuote().multiply(new BigDecimal(10)).divide(new BigDecimal(100));
                    projectCostsDO.setQuotation(internalManagementFee);
                } else if (projectCostsDO.getClassify().equals(ProjectCostsEnum.TAX.getStatus())) {
                    // 计算税费：按立项总报价的5%计算
                    BigDecimal taxFee = project.getSetupQuote().multiply(new BigDecimal(5)).divide(new BigDecimal(100));
                    projectCostsDO.setQuotation(taxFee);
                } else if (projectCostsDO.getClassify().equals(ProjectCostsEnum.PROJECT_MANAGER.getStatus())) {
                    // 计算人力-项目经理：项目执行时间的1/4乘以900
                    if(project.getSubmitTime() != null&& project.getStartTime() != null){
                        // 计算项目执行时间
                        long projectExecutionTim = project.getSubmitTime().getSecond() - project.getStartTime().getSecond();
                        BigDecimal projectManagerFee = BigDecimal.valueOf(projectExecutionTim).divide(new BigDecimal(4)).multiply(new BigDecimal(900));
                        projectCostsDO.setQuotation(projectManagerFee);
                    }else{
                        projectCostsDO.setQuotation(BigDecimal.ZERO);
                    }
                }
                // 给原始数据打标记，方便删除
                clearIds.add(pageResult.getList().indexOf(projectCostsDO));
                // 保存到pageResult里面去
                pageNewResult.getList().add(projectCostsDO);
            }
            if (!pageResult.getList().isEmpty()) {
                // 清除旧数据
                for(ProjectCostsDO projectCostsDO : pageResult.getList()) {
                    if (clearIds.contains(pageResult.getList().indexOf(projectCostsDO))) {
                        // 结果列表清除旧数据
                        pageNewResult.getList().remove(projectCostsDO);
                    }else {
                        // 数据库更新
                        projectCostsMapper.updateById(projectCostsDO);
                    }
                }
            }
        }

        // 分页返回
        return pageResult;
    }

}