package com.cgtiger.erp.module.shuhu.controller.admin.projectcostitems.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 项目成本明细新增/修改 Request VO")
@Data
public class ProjectCostItemsSaveReqVO {

    @Schema(description = "成本记录唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28173")
    private Long id;

    @Schema(description = "展区名称")
    private String exhibitionAreaName;

    @Schema(description = "条目分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "条目分类不能为空")
    private String itemCategory;

    @Schema(description = "展台部位/子项目")
    private String boothPart;

    @Schema(description = "条目名称", example = "王五")
    private String itemName;

    @Schema(description = "框架条目编号")
    private String frameItemCode;

    @Schema(description = "品牌及参考型号")
    private String brandModel;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "工艺要求及效果说明")
    private String processRequire;

    @Schema(description = "尺寸/数量")
    private BigDecimal sizeQuantity;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "单价(不含税)")
    private BigDecimal unitPriceExclTax;

    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "国家系数")
    private BigDecimal nationalCoefficient;

    @Schema(description = "框架条目名称", example = "李四")
    private String frameItemName;

    @Schema(description = "总价(不含税)")
    private BigDecimal totalPriceExclTax;

    @Schema(description = "总价(含税价)")
    private BigDecimal totalPriceInclTax;

    @Schema(description = "物权判断(服务与耗材除外)", example = "你猜")
    private String propertyRightRemark;

}