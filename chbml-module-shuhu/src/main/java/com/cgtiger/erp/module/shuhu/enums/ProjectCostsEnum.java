package com.cgtiger.erp.module.shuhu.enums;

import lombok.Getter;

import java.util.*;

/**
 * 字典sh_manage_class
 */
@Getter
public enum ProjectCostsEnum {
    OVERHEAD(1, "overhead", "公司管理费"),
    INTERNAL_MANAGEMENT(2, "internal_management", "内部管理"),
    TAX(3, "tax", "税费"),
    PROJECT_MANAGER(4, "project_manager", "人力-项目经理");


    private final Integer order;
    private final String status;
    private final String desc;

    // 静态缓存，避免重复计算
    private static final Map<String, ProjectCostsEnum> STATUS_MAP = new HashMap<>();
    private static final List<ProjectCostsEnum> ORDERED_STATUSES = new ArrayList<>();

    static {
        // 初始化状态映射表
        for (ProjectCostsEnum statusEnum : ProjectCostsEnum.values()) {
            STATUS_MAP.put(statusEnum.getStatus(), statusEnum);
        }

        // 按order排序
        ORDERED_STATUSES.addAll(Arrays.asList(ProjectCostsEnum.values()));
        ORDERED_STATUSES.sort(Comparator.comparing(ProjectCostsEnum::getOrder));
    }

    ProjectCostsEnum(Integer order, String status, String desc) {
        this.order = order;
        this.status = status;
        this.desc = desc;
    }

    // 根据状态字符串获取枚举
    public static ProjectCostsEnum getByStatus(String status) {
        ProjectCostsEnum statusEnum = STATUS_MAP.get(status);
        if (statusEnum == null) {
            throw new IllegalArgumentException("未知的项目状态: " + status);
        }
        return statusEnum;
    }

    // 获取下一个状态枚举（基于order排序）
    public ProjectCostsEnum getNextStatusEnum() {
        int currentIndex = ORDERED_STATUSES.indexOf(this);
        if (currentIndex == -1) {
            throw new IllegalStateException("当前状态未找到: " + this.status);
        }

        if (currentIndex >= ORDERED_STATUSES.size() - 1) {
            throw new IllegalStateException("当前状态 [" + this.desc + "] 已是最后一个状态，无法推进");
        }

        return ORDERED_STATUSES.get(currentIndex + 1);
    }

    // 获取上一个状态枚举（基于order排序）
    public ProjectCostsEnum getPrevStatusEnum() {
        int currentIndex = ORDERED_STATUSES.indexOf(this);
        if (currentIndex == -1) {
            throw new IllegalStateException("当前状态未找到: " + this.status);
        }

        if (currentIndex <= 0) {
            throw new IllegalStateException("当前状态 [" + this.desc + "] 已是第一个状态，无法撤销");
        }

        return ORDERED_STATUSES.get(currentIndex - 1);
    }

    // 判断是否可以推进到下一状态
    public boolean canMoveToNext() {
        int currentIndex = ORDERED_STATUSES.indexOf(this);
        return currentIndex != -1 && currentIndex < ORDERED_STATUSES.size() - 1;
    }

    // 判断是否可以撤销到上一状态
    public boolean canMoveToPrev() {
        int currentIndex = ORDERED_STATUSES.indexOf(this);
        return currentIndex > 0;
    }

    // 获取所有状态按order排序
    public static List<ProjectCostsEnum> getOrderedStatuses() {
        return new ArrayList<>(ORDERED_STATUSES);
    }
}
