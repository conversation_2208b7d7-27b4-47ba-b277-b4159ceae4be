package com.cgtiger.erp.module.shuhu.dal.dataobject.projectcostitems;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cgtiger.erp.framework.mybatis.core.dataobject.BaseDO;

/**
 * 项目成本明细 DO
 *
 * <AUTHOR>
 */
@TableName("sh_project_cost_items")
@KeySequence("sh_project_cost_items_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCostItemsDO extends BaseDO {

    /**
     * 成本记录唯一ID
     */
    @TableId
    private Long id;
    /**
     * 展区名称
     */
    private String exhibitionAreaName;
    /**
     * 条目分类
     */
    private String itemCategory;
    /**
     * 展台部位/子项目
     */
    private String boothPart;
    /**
     * 条目名称
     */
    private String itemName;
    /**
     * 框架条目编号
     */
    private String frameItemCode;
    /**
     * 品牌及参考型号
     */
    private String brandModel;
    /**
     * 规格
     */
    private String spec;
    /**
     * 工艺要求及效果说明
     */
    private String processRequire;
    /**
     * 尺寸/数量
     */
    private BigDecimal sizeQuantity;
    /**
     * 单位
     */
    private String unit;
    /**
     * 单价(不含税)
     */
    private BigDecimal unitPriceExclTax;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 国家系数
     */
    private BigDecimal nationalCoefficient;
    /**
     * 框架条目名称
     */
    private String frameItemName;
    /**
     * 总价(不含税)
     */
    private BigDecimal totalPriceExclTax;
    /**
     * 总价(含税价)
     */
    private BigDecimal totalPriceInclTax;
    /**
     * 物权判断(服务与耗材除外)
     */
    private String propertyRightRemark;


}