package com.cgtiger.erp.module.shuhu.service.projectfile;

import java.util.*;

import com.cgtiger.erp.module.shuhu.enums.ProjectFilesDefEnum;
import jakarta.validation.*;
import com.cgtiger.erp.module.shuhu.controller.admin.projectfile.vo.*;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projectfile.ProjectFileDO;
import com.cgtiger.erp.framework.common.pojo.PageResult;
import com.cgtiger.erp.framework.common.pojo.PageParam;

/**
 * 项目附件 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectFileService {

    /**
     * 创建项目附件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectFile(@Valid ProjectFileSaveReqVO createReqVO);

    /**
     * 更新项目附件
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectFile(@Valid ProjectFileSaveReqVO updateReqVO);

    /**
     * 删除项目附件
     *
     * @param id 编号
     */
    void deleteProjectFile(Long id);

    /**
    * 批量删除项目附件
    *
    * @param ids 编号
    */
    void deleteProjectFileListByIds(List<Long> ids);

    /**
     * 获得项目附件
     *
     * @param id 编号
     * @return 项目附件
     */
    ProjectFileDO getProjectFile(Long id);

    /**
     * 获得项目附件分页
     *
     * @param pageReqVO 分页查询
     * @return 项目附件分页
     */
    PageResult<ProjectFileDO> getProjectFilePage(ProjectFilePageReqVO pageReqVO);

    /**
     * 上传时根据场景和级别获取附件定义
     * @param scene 场景
     * @param level 级别
     * @return 附件定义
     */
    List<ProjectFilesDefEnum> getUploadFileItems(String scene, String level);

    /**
     * 预览时根据场景、级别、业务Key、获取附件列表
     * @param scene 场景
     * @param level 级别
     * @param businessKey 业务Key
     * @return 附件列表
     */
    List<ProjectFileRespVO> getPreviewFileItems(String scene, String level, String businessKey,Long projectId);

    /**
     * 获取所有文件定义列表
     *
     * @return 所有文件定义列表
     */
    List<ProjectFilesDefEnum> getAllFileDefinitions();

    /**
     * 根据项目ID获取所有项目附件
     *
     * @param projectId 项目ID
     * @return 项目附件列表
     */
    List<ProjectFileDO> getProjectFilesByProjectId(Long projectId);
}
