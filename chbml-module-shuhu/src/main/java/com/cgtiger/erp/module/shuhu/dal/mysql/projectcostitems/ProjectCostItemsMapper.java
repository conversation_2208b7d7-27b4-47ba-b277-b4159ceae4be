package com.cgtiger.erp.module.shuhu.dal.mysql.projectcostitems;

import java.util.*;

import com.cgtiger.erp.framework.common.pojo.PageResult;
import com.cgtiger.erp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cgtiger.erp.framework.mybatis.core.mapper.BaseMapperX;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projectcostitems.ProjectCostItemsDO;
import org.apache.ibatis.annotations.Mapper;
import com.cgtiger.erp.module.shuhu.controller.admin.projectcostitems.vo.*;

/**
 * 项目成本明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectCostItemsMapper extends BaseMapperX<ProjectCostItemsDO> {

    default PageResult<ProjectCostItemsDO> selectPage(ProjectCostItemsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectCostItemsDO>()
                .eqIfPresent(ProjectCostItemsDO::getItemCategory, reqVO.getItemCategory())
                .eqIfPresent(ProjectCostItemsDO::getExhibitionAreaName, reqVO.getExhibitionAreaName())
                .eqIfPresent(ProjectCostItemsDO::getBoothPart, reqVO.getBoothPart())
                .likeIfPresent(ProjectCostItemsDO::getItemName, reqVO.getItemName())
                .eqIfPresent(ProjectCostItemsDO::getFrameItemCode, reqVO.getFrameItemCode())
                .eqIfPresent(ProjectCostItemsDO::getBrandModel, reqVO.getBrandModel())
                .eqIfPresent(ProjectCostItemsDO::getSpec, reqVO.getSpec())
                .eqIfPresent(ProjectCostItemsDO::getProcessRequire, reqVO.getProcessRequire())
                .eqIfPresent(ProjectCostItemsDO::getSizeQuantity, reqVO.getSizeQuantity())
                .eqIfPresent(ProjectCostItemsDO::getUnit, reqVO.getUnit())
                .eqIfPresent(ProjectCostItemsDO::getUnitPriceExclTax, reqVO.getUnitPriceExclTax())
                .eqIfPresent(ProjectCostItemsDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(ProjectCostItemsDO::getNationalCoefficient, reqVO.getNationalCoefficient())
                .likeIfPresent(ProjectCostItemsDO::getFrameItemName, reqVO.getFrameItemName())
                .eqIfPresent(ProjectCostItemsDO::getTotalPriceExclTax, reqVO.getTotalPriceExclTax())
                .eqIfPresent(ProjectCostItemsDO::getTotalPriceInclTax, reqVO.getTotalPriceInclTax())
                .eqIfPresent(ProjectCostItemsDO::getPropertyRightRemark, reqVO.getPropertyRightRemark())
                .betweenIfPresent(ProjectCostItemsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectCostItemsDO::getId));
    }

}