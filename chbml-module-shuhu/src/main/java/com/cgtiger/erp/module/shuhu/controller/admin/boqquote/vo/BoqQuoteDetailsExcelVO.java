package com.cgtiger.erp.module.shuhu.controller.admin.boqquote.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cgtiger.erp.module.shuhu.dal.dataobject.boqquote.BoqQuoteDO;
import com.cgtiger.erp.module.shuhu.dal.dataobject.projectsdemanditem.ProjectsDemandItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - BOQ报价详情 Excel VO")
@Data
@ExcelIgnoreUnannotated
public class BoqQuoteDetailsExcelVO {

    /**
     * 主键ID
     */
    @ExcelProperty("主键ID")
    @TableId
    private Long id;
    /**
     * 数据来源（需求条目ID）
     */
    @ExcelProperty("数据来源（需求条目ID）")
    private Long sourceId;
    /**
     * 项目ID
     */
    @ExcelProperty("项目ID")
    private Long projectId;
    /**
     * 报价未税单价
     */
    @ExcelProperty("报价未税单价")
    private BigDecimal quoteUnitPriceExTax;
    /**
     * 报价未税合计
     */
    @ExcelProperty("报价未税合计")
    private BigDecimal quoteTotalExTax;
    /**
     * 报价税率
     */
    @ExcelProperty("报价税率")
    private BigDecimal quoteTaxRate;
    /**
     * 报价含税合计金额
     */
    @ExcelProperty("报价含税合计金额")
    private BigDecimal quoteTotalInclTax;
    /**
     * 成本单价
     */
    @ExcelProperty("成本单价")
    private BigDecimal costUnitPrice;
    /**
     * 成本合计金额
     */
    @ExcelProperty("成本合计金额")
    private BigDecimal costTotalAmount;
    /**
     * 成本税率
     */
    @ExcelProperty("成本税率")
    private BigDecimal costTaxRate;
    /**
     * 成本分类
     */
    @ExcelProperty("成本分类")
    private String costCategory;

//    ----------------

    /**
     * 需求ID
     */
    @ExcelProperty("需求ID")
    private Long demandId;
    /**
     * 条目名称
     */
    @ExcelProperty("条目名称")
    private String itemName;
    /**
     * 条目关键字
     */
    @ExcelProperty("条目关键字")
    private String itemKey;
    /**
     * 框架条目编号
     */
    @ExcelProperty("框架条目编号")
    private String frameworkNumber;
    /**
     * 品牌及参考型号
     */
    @ExcelProperty("品牌及参考型号")
    private String model;
    /**
     * 规格
     */
    @ExcelProperty("规格")
    private String spec;
    /**
     * 工艺要求及效果说明
     */
    @ExcelProperty("工艺要求及效果说明")
    private String details;
    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;
    /**
     * 尺寸/数量
     */
    @ExcelProperty("尺寸/数量")
    private String quantity;
    /**
     * 国家系数
     */
    @ExcelProperty("国家系数")
    private String nationalCoefficient;
}
