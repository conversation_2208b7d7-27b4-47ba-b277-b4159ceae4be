package com.cgtiger.erp.module.shuhu.controller.admin.boqquote;

import com.cgtiger.erp.module.shuhu.dal.dataobject.boqquote.BoqQuoteDetailsDO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.cgtiger.erp.framework.common.pojo.PageParam;
import com.cgtiger.erp.framework.common.pojo.PageResult;
import com.cgtiger.erp.framework.common.pojo.CommonResult;
import com.cgtiger.erp.framework.common.util.object.BeanUtils;
import static com.cgtiger.erp.framework.common.pojo.CommonResult.success;

import com.cgtiger.erp.framework.excel.core.util.ExcelUtils;

import com.cgtiger.erp.framework.apilog.core.annotation.ApiAccessLog;
import static com.cgtiger.erp.framework.apilog.core.enums.OperateTypeEnum.*;

import com.cgtiger.erp.module.shuhu.controller.admin.boqquote.vo.*;
import com.cgtiger.erp.module.shuhu.dal.dataobject.boqquote.BoqQuoteDO;
import com.cgtiger.erp.module.shuhu.service.boqquote.BoqQuoteService;

@Tag(name = "管理后台 - BOQ报价")
@RestController
@RequestMapping("/shuhu/boq-quote")
@Validated
public class BoqQuoteController {

    @Resource
    private BoqQuoteService boqQuoteService;

    @PostMapping("/create")
    @Operation(summary = "创建BOQ报价")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:create')")
    public CommonResult<Long> createBoqQuote(@Valid @RequestBody BoqQuoteSaveReqVO createReqVO) {
        return success(boqQuoteService.createBoqQuote(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新BOQ报价")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:update')")
    public CommonResult<Boolean> updateBoqQuote(@Valid @RequestBody BoqQuoteSaveReqVO updateReqVO) {
        boqQuoteService.updateBoqQuote(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除BOQ报价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:delete')")
    public CommonResult<Boolean> deleteBoqQuote(@RequestParam("id") Long id) {
        boqQuoteService.deleteBoqQuote(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除BOQ报价")
                @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:delete')")
    public CommonResult<Boolean> deleteBoqQuoteList(@RequestParam("ids") List<Long> ids) {
        boqQuoteService.deleteBoqQuoteListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得BOQ报价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:query')")
    public CommonResult<BoqQuoteRespVO> getBoqQuote(@RequestParam("id") Long id) {
        BoqQuoteDO boqQuote = boqQuoteService.getBoqQuote(id);
        return success(BeanUtils.toBean(boqQuote, BoqQuoteRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得BOQ报价分页")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:query')")
    public CommonResult<PageResult<BoqQuoteRespVO>> getBoqQuotePage(@Valid BoqQuotePageReqVO pageReqVO) {
        PageResult<BoqQuoteDO> pageResult = boqQuoteService.getBoqQuotePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BoqQuoteRespVO.class));
    }

    @GetMapping("/all")
    @Operation(summary = "获得BOQ报价列表")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:query')")
    public CommonResult<List<BoqQuoteDetailsDO>> getBoqQuoteList(@Valid BoqQuotePageReqVO pageReqVO) {
        List<BoqQuoteDetailsDO> list = boqQuoteService.getBoqQuoteList(pageReqVO);
        return success(list);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出BOQ报价 Excel")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBoqQuoteExcel(@Valid BoqQuotePageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BoqQuoteDetailsDO> list = boqQuoteService.getBoqQuoteList(pageReqVO);
        // 转换为Excel DTO
        List<BoqQuoteDetailsExcelVO> excelList = convertToExcelDTO(list);
        // 导出 Excel
        ExcelUtils.write(response, "BOQ报价.xls", "数据", BoqQuoteDetailsExcelVO.class, excelList);
    }

    @PutMapping("/sync-quote")
    @Operation(summary = "同步BOQ报价")
    @PreAuthorize("@ss.hasPermission('shuhu:boq-quote:create')")
    public CommonResult<Boolean> syncQuote(@Valid @RequestParam("projectId") Long projectId) {
        boqQuoteService.syncQuote(projectId);
        return success(true);
    }

    /**
     * 转换为Excel DTO
     */
    private List<BoqQuoteDetailsExcelVO> convertToExcelDTO(List<BoqQuoteDetailsDO> list) {
        List<BoqQuoteDetailsExcelVO> result = new ArrayList<>();
        for (BoqQuoteDetailsDO item : list) {
            BoqQuoteDetailsExcelVO excelVO = new BoqQuoteDetailsExcelVO();

            // 设置内部字段（不导出到Excel）
            excelVO.setId(item.getId());
            excelVO.setSourceId(item.getSourceId());
            excelVO.setProjectId(item.getProjectId());
            excelVO.setCostUnitPrice(item.getCostUnitPrice());
            excelVO.setCostTotalAmount(item.getCostTotalAmount());
            excelVO.setCostTaxRate(item.getCostTaxRate());

            // 设置Excel导出字段
            if (item.getDemandItemDO() != null) {
                excelVO.setDemandId(item.getDemandItemDO().getDemandId());
                excelVO.setItemKey(item.getDemandItemDO().getItemKey());

                // 按照指定顺序设置Excel字段
                excelVO.setExhibitionAreaName(""); // 展区名称 - 暂时为空，需要从其他地方获取
                excelVO.setCostCategory(item.getCostCategory()); // 条目分类
                excelVO.setSubProject(""); // 展台部位/子项目 - 暂时为空，需要从其他地方获取
                excelVO.setItemName(item.getDemandItemDO().getItemName()); // 条目名称
                excelVO.setFrameworkNumber(item.getDemandItemDO().getFrameworkNumber()); // 框架条目编号
                excelVO.setModel(item.getDemandItemDO().getModel()); // 品牌及参考型号
                excelVO.setSpec(item.getDemandItemDO().getSpec()); // 规格
                excelVO.setDetails(item.getDemandItemDO().getDetails()); // 工艺要求及效果说明
                excelVO.setQuantity(item.getDemandItemDO().getQuantity()); // 尺寸/数量
                excelVO.setUnit(item.getDemandItemDO().getUnit()); // 单位
                excelVO.setQuoteUnitPriceExTax(item.getQuoteUnitPriceExTax()); // 单价(不含税)
                excelVO.setQuoteTaxRate(item.getQuoteTaxRate()); // 税率（%）
                excelVO.setNationalCoefficient(item.getDemandItemDO().getNationalCoefficient()); // 国家系数
                excelVO.setFrameworkItemName(""); // 框架条目名称 - 暂时为空，需要从其他地方获取
                excelVO.setQuoteTotalExTax(item.getQuoteTotalExTax()); // 总价（不含税）
                excelVO.setQuoteTotalInclTax(item.getQuoteTotalInclTax()); // 总价（含税价）
                excelVO.setRemark(""); // 备注 - 暂时为空
                excelVO.setPropertyRightsJudgment(""); // *物权判断(服务与耗材除外） - 暂时为空
                excelVO.setCategoryAnalysis(""); // 条目分类分析 - 暂时为空
                excelVO.setReuseAmountExTax(null); // 复用计算含复用金额（不含税） - 暂时为空
                excelVO.setFrameworkPropertyCheck(""); // 框架条目物权check - 暂时为空
            } else {
                // 如果没有需求条目数据，设置基本字段
                excelVO.setExhibitionAreaName("");
                excelVO.setCostCategory(item.getCostCategory());
                excelVO.setSubProject("");
                excelVO.setQuoteUnitPriceExTax(item.getQuoteUnitPriceExTax());
                excelVO.setQuoteTaxRate(item.getQuoteTaxRate());
                excelVO.setQuoteTotalExTax(item.getQuoteTotalExTax());
                excelVO.setQuoteTotalInclTax(item.getQuoteTotalInclTax());
            }

            result.add(excelVO);
        }
        return result;
    }
}