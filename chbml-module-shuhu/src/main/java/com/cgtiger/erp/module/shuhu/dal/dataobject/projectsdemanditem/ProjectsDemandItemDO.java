package com.cgtiger.erp.module.shuhu.dal.dataobject.projectsdemanditem;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cgtiger.erp.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数虎项目需求条目 DO
 *
 * <AUTHOR>
 */
@TableName("sh_projects_demand_item")
@KeySequence("sh_projects_demand_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectsDemandItemDO extends BaseDO {

    /**
     * 主键ID
     */
    @ExcelProperty("主键ID")
    @TableId
    private Long id;

    /**
     * 需求ID
     */
    @ExcelProperty("需求ID")
    private Long demandId;

    /**
     * 条目名称
     */
    @ExcelProperty("条目名称")
    private String itemName;

    /**
     * 条目关键字
     */
    @ExcelProperty("条目关键字")
    private String itemKey;

    /**
     * 框架条目编号
     */
    @ExcelProperty("框架条目编号")
    private String frameworkNumber;

    /**
     * 品牌及参考型号
     */
    @ExcelProperty("品牌及参考型号")
    private String model;

    /**
     * 规格
     */
    @ExcelProperty("规格")
    private String spec;

    /**
     * 工艺要求及效果说明
     */
    @ExcelProperty("工艺要求及效果说明")
    private String details;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;

    /**
     * 尺寸/数量
     */
    @ExcelProperty("尺寸/数量")
    private String quantity;

    /**
     * 单价(不含税)
     */
    @ExcelProperty("单价(不含税)")
    private BigDecimal quoteUnitPriceExTax;

    /**
     * 国家系数
     */
    @ExcelProperty("国家系数")
    private String nationalCoefficient;

    /**
     * 总价(不含税)
     */
    @ExcelProperty("总价(不含税)")
    private BigDecimal quoteTotalExTax;

    /**
     * 税率(%)
     */
    @ExcelProperty("税率(%)")
    private BigDecimal quoteTaxRate;

    /**
     * 成本单价
     */
    @ExcelProperty("成本单价")
    private BigDecimal costUnitPrice;

    /**
     * 成本合计金额
     */
    @ExcelProperty("成本合计金额")
    private BigDecimal costTotalAmount;

    /**
     * 成本税率
     */
    @ExcelProperty("成本税率")
    private BigDecimal costTaxRate;

    /**
     * 总价(含税价)
     */
    @ExcelProperty("总价(含税价)")
    private BigDecimal quoteTotalInclTax;

    /**
     * 成本分类
     */
    @ExcelProperty("成本分类")
    private String costCategory;

}