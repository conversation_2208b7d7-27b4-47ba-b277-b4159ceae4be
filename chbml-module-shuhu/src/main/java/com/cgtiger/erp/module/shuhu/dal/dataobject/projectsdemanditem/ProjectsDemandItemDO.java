package com.cgtiger.erp.module.shuhu.dal.dataobject.projectsdemanditem;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.cgtiger.erp.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数虎项目需求条目 DO
 *
 * <AUTHOR>
 */
@TableName("sh_projects_demand_item")
@KeySequence("sh_projects_demand_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectsDemandItemDO extends BaseDO {

    /**
     * 主键ID
     */
    @ExcelProperty("主键ID")
    @TableId
    private Long id;

}