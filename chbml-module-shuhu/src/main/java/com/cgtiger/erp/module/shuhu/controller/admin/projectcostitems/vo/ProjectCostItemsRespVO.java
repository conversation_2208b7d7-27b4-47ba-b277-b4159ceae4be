package com.cgtiger.erp.module.shuhu.controller.admin.projectcostitems.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 项目成本明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectCostItemsRespVO {

    @Schema(description = "成本记录唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28173")
    @ExcelProperty("成本记录唯一ID")
    private Long id;

    @Schema(description = "展区名称")
    @ExcelProperty("展区名称")
    private String exhibitionAreaName;

    @Schema(description = "条目分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条目分类")
    private String itemCategory;

    @Schema(description = "展台部位/子项目")
    @ExcelProperty("展台部位/子项目")
    private String boothPart;

    @Schema(description = "条目名称", example = "王五")
    @ExcelProperty("条目名称")
    private String itemName;

    @Schema(description = "框架条目编号")
    @ExcelProperty("框架条目编号")
    private String frameItemCode;

    @Schema(description = "品牌及参考型号")
    @ExcelProperty("品牌及参考型号")
    private String brandModel;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String spec;

    @Schema(description = "工艺要求及效果说明")
    @ExcelProperty("工艺要求及效果说明")
    private String processRequire;

    @Schema(description = "尺寸/数量")
    @ExcelProperty("尺寸/数量")
    private BigDecimal sizeQuantity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String unit;

    @Schema(description = "单价(不含税)")
    @ExcelProperty("单价(不含税)")
    private BigDecimal unitPriceExclTax;

    @Schema(description = "税率")
    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @Schema(description = "国家系数")
    @ExcelProperty("国家系数")
    private BigDecimal nationalCoefficient;

    @Schema(description = "框架条目名称", example = "李四")
    @ExcelProperty("框架条目名称")
    private String frameItemName;

    @Schema(description = "总价(不含税)")
    @ExcelProperty("总价(不含税)")
    private BigDecimal totalPriceExclTax;

    @Schema(description = "总价(含税价)")
    @ExcelProperty("总价(含税价)")
    private BigDecimal totalPriceInclTax;

    @Schema(description = "物权判断(服务与耗材除外)", example = "你猜")
    @ExcelProperty("物权判断(服务与耗材除外)")
    private String propertyRightRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}